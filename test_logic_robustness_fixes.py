#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
逻辑与健壮性问题修复测试脚本
测试SearchManager的智能搜索逻辑和改进的异常处理
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_search_scope_determination():
    """测试智能搜索模式选择"""
    print("🧪 测试智能搜索模式选择")
    print("=" * 60)
    
    try:
        # 导入SearchManager
        from ai_report_complete_0728.search.search_manager import SearchManager
        
        # 创建一个模拟的generator对象
        class MockGenerator:
            pass
        
        generator = MockGenerator()
        search_manager = SearchManager(generator)
        
        # 测试用例：应该使用网页搜索的查询
        webpage_queries = [
            "地热发电市场规模",
            "地热能源投资机会", 
            "地热发电公司排名",
            "地热能源政策支持",
            "新能源汽车销量数据",
            "人工智能商业应用"
        ]
        
        # 测试用例：应该使用学术搜索的查询
        scholar_queries = [
            "地热发电技术研究",
            "地热能源学术论文",
            "地热发电实验数据",
            "地热能源科学分析",
            "机器学习算法研究",
            "深度学习理论模型",
            "人工智能技术创新",
            "大学研究所报告"
        ]
        
        print("📊 测试网页搜索模式判断...")
        success_count = 0
        for query in webpage_queries:
            scope = search_manager._determine_search_scope(query)
            if scope == 'webpage':
                print(f"   ✅ '{query}' → {scope} (正确)")
                success_count += 1
            else:
                print(f"   ❌ '{query}' → {scope} (错误，应为webpage)")
        
        print(f"\n📚 测试学术搜索模式判断...")
        for query in scholar_queries:
            scope = search_manager._determine_search_scope(query)
            if scope == 'scholar':
                print(f"   ✅ '{query}' → {scope} (正确)")
                success_count += 1
            else:
                print(f"   ❌ '{query}' → {scope} (错误，应为scholar)")
        
        total_tests = len(webpage_queries) + len(scholar_queries)
        print(f"\n📈 测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("🎉 智能搜索模式选择测试完全通过！")
            return True
        else:
            print("⚠️ 部分测试未通过，需要调整判断逻辑")
            return False
            
    except Exception as e:
        print(f"❌ 智能搜索模式测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_multi_source_search_logic():
    """测试多源搜索逻辑是否使用了智能判断"""
    print("\n🧪 测试多源搜索逻辑")
    print("=" * 60)
    
    try:
        from ai_report_complete_0728.search.search_manager import SearchManager
        import inspect
        
        # 创建一个模拟的generator对象
        class MockGenerator:
            pass
        
        generator = MockGenerator()
        search_manager = SearchManager(generator)
        
        # 检查 multi_source_search 方法是否使用了 _determine_search_scope
        source = inspect.getsource(search_manager.multi_source_search)
        
        if '_determine_search_scope' in source:
            print("✅ multi_source_search 正确使用了 _determine_search_scope")
            print("✅ 智能搜索逻辑已启用")
            return True
        else:
            print("❌ multi_source_search 没有使用 _determine_search_scope")
            print("❌ 智能搜索逻辑未启用")
            return False
            
    except Exception as e:
        print(f"❌ 多源搜索逻辑测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_exception_handling_improvements():
    """测试异常处理改进"""
    print("\n🧪 测试异常处理改进")
    print("=" * 60)
    
    try:
        # 读取SearchManager源码
        search_manager_path = project_root / "ai_report_complete_0728" / "search" / "search_manager.py"
        
        with open(search_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计异常处理模式
        import re
        
        # 统计具体异常处理
        specific_exceptions = len(re.findall(r'except \([^)]+\) as e:', content))
        requests_exceptions = len(re.findall(r'except requests\.exceptions\.\w+', content))
        json_exceptions = len(re.findall(r'except.*JSONDecodeError', content))
        http_exceptions = len(re.findall(r'except.*HTTPException', content))
        
        # 统计宽泛异常处理（应该作为兜底）
        broad_exceptions = len(re.findall(r'except Exception as e:', content))
        
        print(f"📊 异常处理统计:")
        print(f"   具体异常处理 (多类型): {specific_exceptions}")
        print(f"   Requests异常处理: {requests_exceptions}")
        print(f"   JSON异常处理: {json_exceptions}")
        print(f"   HTTP异常处理: {http_exceptions}")
        print(f"   宽泛异常处理 (兜底): {broad_exceptions}")
        
        # 检查是否有改进
        if specific_exceptions > 0 and requests_exceptions > 0:
            print("✅ 异常处理已改进，使用了具体的异常类型")
            return True
        else:
            print("❌ 异常处理仍需改进")
            return False
            
    except Exception as e:
        print(f"❌ 异常处理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_error_recovery_mechanisms():
    """测试错误恢复机制"""
    print("\n🧪 测试错误恢复机制")
    print("=" * 60)
    
    try:
        from ai_report_complete_0728.search.search_manager import SearchManager
        
        # 创建一个模拟的generator对象
        class MockGenerator:
            pass
        
        generator = MockGenerator()
        search_manager = SearchManager(generator)
        
        # 测试无效查询的处理
        print("📝 测试空查询处理...")
        result = search_manager.multi_source_search("", ['metaso'], 5)
        if isinstance(result, list):
            print("   ✅ 空查询返回空列表，处理正确")
        else:
            print("   ❌ 空查询处理异常")
            return False
        
        # 测试无效搜索类型的处理
        print("📝 测试无效搜索类型处理...")
        result = search_manager.multi_source_search("测试查询", ['invalid_engine'], 5)
        if isinstance(result, list):
            print("   ✅ 无效搜索引擎返回空列表，处理正确")
        else:
            print("   ❌ 无效搜索引擎处理异常")
            return False
        
        print("✅ 错误恢复机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误恢复机制测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始逻辑与健壮性问题修复测试")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试智能搜索模式选择
    test_results.append(test_search_scope_determination())
    
    # 2. 测试多源搜索逻辑
    test_results.append(test_multi_source_search_logic())
    
    # 3. 测试异常处理改进
    test_results.append(test_exception_handling_improvements())
    
    # 4. 测试错误恢复机制
    test_results.append(test_error_recovery_mechanisms())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    test_names = [
        "智能搜索模式选择",
        "多源搜索逻辑",
        "异常处理改进",
        "错误恢复机制"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有逻辑与健壮性问题修复测试通过！")
        print("\n✅ 修复成果:")
        print("1. ✅ 智能搜索逻辑已启用并正常工作")
        print("2. ✅ 异常处理更加具体和健壮")
        print("3. ✅ 错误恢复机制工作正常")
        print("4. ✅ 代码质量显著提升")
    else:
        print("⚠️ 部分测试未通过，需要进一步修复")
        
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
