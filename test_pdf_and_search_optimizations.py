#!/usr/bin/env python3
"""
测试PDF缓存机制和搜索功能优化
"""

import sys
import os
from pathlib import Path
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_search_auto_confirm():
    """测试搜索自动确认功能"""
    print("🧪 测试搜索自动确认功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 启用自动搜索确认
        generator.report_config["search_auto_confirm"] = True
        
        print(f"   ⚙️ 自动搜索确认: {generator.report_config.get('search_auto_confirm', False)}")
        
        # 创建模拟报告内容
        mock_report_content = """
        # 地热发电产业研究报告
        
        ## 1. 技术概述
        地热发电是一种利用地热能发电的技术...
        
        ## 2. 市场现状
        当前地热发电市场发展迅速...
        """
        
        # 创建临时报告文件
        temp_report = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md', encoding='utf-8')
        temp_report.write(mock_report_content)
        temp_report.close()
        
        print(f"   📄 创建临时报告: {temp_report.name}")
        
        # 测试搜索增强（应该自动确认）
        print("   🔍 测试搜索增强（自动确认模式）...")
        
        try:
            # 这应该不会要求用户输入
            enhanced_path = generator.enhance_report_with_search(
                temp_report.name, 
                "地热发电产业研究", 
                user_confirm=True  # 即使设置为True，也应该自动确认
            )
            
            if enhanced_path:
                print(f"   ✅ 搜索增强完成: {enhanced_path}")
                success = True
            else:
                print(f"   ⚠️ 搜索增强未执行（可能无内容缺口）")
                success = True  # 这也是正常情况
                
        except Exception as e:
            print(f"   ❌ 搜索增强失败: {str(e)}")
            success = False
        
        # 清理临时文件
        try:
            os.unlink(temp_report.name)
            print(f"   🗑️ 清理临时报告")
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality_integration():
    """测试搜索功能集成"""
    print("🧪 测试搜索功能集成...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 启用搜索功能和自动确认
        generator.report_config["enable_search_enhancement"] = True
        generator.report_config["search_auto_confirm"] = True
        
        print(f"   ⚙️ 搜索增强: {generator.report_config.get('enable_search_enhancement', False)}")
        print(f"   ⚙️ 自动确认: {generator.report_config.get('search_auto_confirm', False)}")
        
        # 测试搜索管理器初始化
        search_manager = generator.SearchManager(generator)
        
        print(f"   📡 可用搜索API: {list(search_manager.search_apis.keys())}")
        
        if search_manager.search_apis:
            # 测试简单搜索
            test_query = "地热发电技术发展"
            print(f"   🔍 测试搜索查询: {test_query}")
            
            results = search_manager.multi_source_search(test_query, ['metaso'], num_results=2)
            
            if results:
                print(f"   ✅ 搜索成功: {len(results)} 个结果")
                for i, result in enumerate(results[:2], 1):
                    print(f"     {i}. {result.get('title', '无标题')[:50]}...")
                return True
            else:
                print(f"   ⚠️ 搜索未返回结果")
                return True  # 这也可能是正常的
        else:
            print(f"   ❌ 没有可用的搜索API")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_cache_structure():
    """测试PDF缓存结构"""
    print("🧪 测试PDF缓存结构...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试缓存检查方法
        test_pdf_path = Path("test_sample.pdf")
        
        # 测试缓存检查（应该返回None，因为文件不存在）
        cache_result = generator._check_new_pdf_cache(test_pdf_path)
        
        if cache_result is None:
            print(f"   ✅ 缓存检查正常：不存在的文件返回None")
        else:
            print(f"   ❌ 缓存检查异常：不存在的文件应该返回None")
            return False
        
        # 测试缓存路径构建
        cache_base_dir = Path("cache/pdf_cache")
        pdf_cache_dir = cache_base_dir / test_pdf_path.stem
        text_dir = pdf_cache_dir / "text"
        images_dir = pdf_cache_dir / "images"
        
        print(f"   📁 缓存结构设计:")
        print(f"     基础目录: {cache_base_dir}")
        print(f"     PDF目录: {pdf_cache_dir}")
        print(f"     文字目录: {text_dir}")
        print(f"     图片目录: {images_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试PDF缓存和搜索功能优化")
    print("=" * 60)
    
    # 测试PDF缓存结构
    test1_result = test_pdf_cache_structure()
    print()
    
    # 测试搜索自动确认
    test2_result = test_search_auto_confirm()
    print()
    
    # 测试搜索功能集成
    test3_result = test_search_functionality_integration()
    print()
    
    # 总结
    print("=" * 60)
    print("📊 测试结果总结:")
    print(f"   PDF缓存结构: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   搜索自动确认: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   搜索功能集成: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = all([test1_result, test2_result, test3_result])
    
    if all_passed:
        print("\n🎉 所有测试通过！优化功能正常工作。")
        print("\n📋 使用说明:")
        print("   1. PDF文件将自动缓存到 cache/pdf_cache/[文件名]/ 目录")
        print("   2. 每个PDF对应一个文件夹，包含text/和images/两个子目录")
        print("   3. 搜索功能支持自动确认，设置 search_auto_confirm=True 即可")
        print("\n⚙️ 配置示例:")
        print("   generator = CompleteReportGenerator()")
        print("   generator.report_config['search_auto_confirm'] = True")
        print("   # 这样搜索功能就不会要求用户输入确认了")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查具体问题。")
        return False

if __name__ == "__main__":
    main()
