#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试代码重复消除和逻辑一致性修复
验证基类重构和SearchManager逻辑修复的效果
"""

import sys
import traceback
import inspect

def test_base_class_inheritance():
    """测试基类继承是否正确"""
    print("🧪 测试基类继承")
    print("=" * 60)
    
    try:
        from complete_report_generator import (
            BaseGeminiAPIManager, 
            GeminiAPIManager, 
            AsyncGeminiAPIManager,
            API_KEYS, 
            MODEL_NAMES, 
            GeminiModelConfig
        )
        
        print("✅ 成功导入所有类")
        
        # 检查继承关系
        print("\n🔍 检查继承关系:")
        print(f"   GeminiAPIManager 继承自: {GeminiAPIManager.__bases__}")
        print(f"   AsyncGeminiAPIManager 继承自: {AsyncGeminiAPIManager.__bases__}")
        
        # 验证基类方法存在
        base_methods = ['_extract_task_purpose', '_extract_title_from_prompt', '_extract_level_from_prompt']
        
        print("\n🔍 检查基类方法:")
        for method_name in base_methods:
            if hasattr(BaseGeminiAPIManager, method_name):
                print(f"   ✅ BaseGeminiAPIManager.{method_name} 存在")
            else:
                print(f"   ❌ BaseGeminiAPIManager.{method_name} 不存在")
                return False
        
        # 验证子类可以访问基类方法
        print("\n🔍 检查子类方法访问:")
        
        # 创建实例
        model_config = GeminiModelConfig()
        sync_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        async_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        
        for method_name in base_methods:
            # 检查同步管理器
            if hasattr(sync_manager, method_name):
                print(f"   ✅ GeminiAPIManager.{method_name} 可访问")
            else:
                print(f"   ❌ GeminiAPIManager.{method_name} 不可访问")
                return False
            
            # 检查异步管理器
            if hasattr(async_manager, method_name):
                print(f"   ✅ AsyncGeminiAPIManager.{method_name} 可访问")
            else:
                print(f"   ❌ AsyncGeminiAPIManager.{method_name} 不可访问")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 基类继承测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_method_functionality():
    """测试方法功能是否正常"""
    print("\n🧪 测试方法功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import GeminiAPIManager, API_KEYS, MODEL_NAMES, GeminiModelConfig
        
        # 创建实例
        model_config = GeminiModelConfig()
        manager = GeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        
        # 测试 _extract_task_purpose
        test_prompts = [
            ("生成报告框架的JSON结构", "🎯 统筹模型生成报告框架结构"),
            ("审核章节「技术发展」的内容质量", "🔍 统筹模型审核章节: 技术发展"),
            ("优化章节《市场分析》的内容", "✨ 统筹模型优化章节: 市场分析"),
            ("生成第3级节点的详细内容", "⚡ 执行模型生成第3级节点: 未知节点"),
        ]
        
        print("🔍 测试 _extract_task_purpose 方法:")
        for prompt, expected in test_prompts:
            result = manager._extract_task_purpose(prompt)
            if expected in result:
                print(f"   ✅ '{prompt[:30]}...' → {result}")
            else:
                print(f"   ❌ '{prompt[:30]}...' → {result} (期望包含: {expected})")
                return False
        
        # 测试 _extract_title_from_prompt
        title_tests = [
            ('"技术发展趋势"', "技术发展趋势"),
            ('「市场分析报告」', "市场分析报告"),
            ('《产业链分析》', "产业链分析"),
            ('【竞争格局】', "竞争格局"),
        ]
        
        print("\n🔍 测试 _extract_title_from_prompt 方法:")
        for prompt, expected in title_tests:
            result = manager._extract_title_from_prompt(prompt)
            if result == expected:
                print(f"   ✅ '{prompt}' → '{result}'")
            else:
                print(f"   ❌ '{prompt}' → '{result}' (期望: '{expected}')")
                return False
        
        # 测试 _extract_level_from_prompt
        level_tests = [
            ("生成第1级标题", "1"),
            ("处理第3级节点", "3"),
            ("第5级内容生成", "5"),
        ]
        
        print("\n🔍 测试 _extract_level_from_prompt 方法:")
        for prompt, expected in level_tests:
            result = manager._extract_level_from_prompt(prompt)
            if result == expected:
                print(f"   ✅ '{prompt}' → '{result}'")
            else:
                print(f"   ❌ '{prompt}' → '{result}' (期望: '{expected}')")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 方法功能测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_search_manager_logic():
    """测试SearchManager逻辑一致性"""
    print("\n🧪 测试SearchManager逻辑一致性")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        search_manager = generator.SearchManager(generator)
        
        print("✅ 成功创建SearchManager实例")
        
        # 检查是否有 _determine_search_scope 方法
        if hasattr(search_manager, '_determine_search_scope'):
            print("✅ _determine_search_scope 方法存在")
        else:
            print("❌ _determine_search_scope 方法不存在")
            return False
        
        # 测试智能搜索模式选择
        test_queries = [
            ("地热发电市场规模", "webpage"),  # 应该是网页搜索
            ("地热发电技术研究", "scholar"),  # 应该是学术搜索
            ("地热能源学术论文", "scholar"),  # 应该是学术搜索
            ("地热发电公司排名", "webpage"),  # 应该是网页搜索
        ]
        
        print("\n🔍 测试智能搜索模式选择:")
        for query, expected_scope in test_queries:
            try:
                scope = search_manager._determine_search_scope(query)
                if scope == expected_scope:
                    print(f"   ✅ '{query}' → {scope}")
                else:
                    print(f"   ⚠️ '{query}' → {scope} (期望: {expected_scope})")
            except Exception as e:
                print(f"   ❌ '{query}' → 错误: {str(e)}")
                return False
        
        # 检查 multi_source_search 是否使用了 _determine_search_scope
        print("\n🔍 检查 multi_source_search 实现:")
        
        # 获取方法源码
        try:
            source = inspect.getsource(search_manager.multi_source_search)
            if '_determine_search_scope' in source:
                print("   ✅ multi_source_search 使用了 _determine_search_scope")
            else:
                print("   ❌ multi_source_search 没有使用 _determine_search_scope")
                return False
        except Exception as e:
            print(f"   ⚠️ 无法检查源码: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ SearchManager逻辑测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_code_deduplication_effectiveness():
    """测试代码去重效果"""
    print("\n🧪 测试代码去重效果")
    print("=" * 60)
    
    try:
        from complete_report_generator import (
            BaseGeminiAPIManager, 
            GeminiAPIManager, 
            AsyncGeminiAPIManager
        )
        
        # 检查方法是否只在基类中定义
        base_methods = ['_extract_task_purpose', '_extract_title_from_prompt', '_extract_level_from_prompt']
        
        print("🔍 检查方法定义位置:")
        for method_name in base_methods:
            # 检查基类
            base_has_method = method_name in BaseGeminiAPIManager.__dict__
            sync_has_method = method_name in GeminiAPIManager.__dict__
            async_has_method = method_name in AsyncGeminiAPIManager.__dict__
            
            print(f"   {method_name}:")
            print(f"     BaseGeminiAPIManager: {'✅' if base_has_method else '❌'}")
            print(f"     GeminiAPIManager: {'❌ (重复)' if sync_has_method else '✅ (继承)'}")
            print(f"     AsyncGeminiAPIManager: {'❌ (重复)' if async_has_method else '✅ (继承)'}")
            
            if not base_has_method:
                print(f"   ❌ {method_name} 不在基类中")
                return False
            
            if sync_has_method or async_has_method:
                print(f"   ❌ {method_name} 存在重复定义")
                return False
        
        print("\n✅ 代码去重成功，所有方法只在基类中定义")
        return True
        
    except Exception as e:
        print(f"❌ 代码去重效果测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始代码重复消除和逻辑一致性测试")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("基类继承", test_base_class_inheritance),
        ("方法功能", test_method_functionality),
        ("SearchManager逻辑", test_search_manager_logic),
        ("代码去重效果", test_code_deduplication_effectiveness),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试执行失败: {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！代码重复消除和逻辑一致性修复成功！")
        print("✅ 基类重构完成，消除了代码重复")
        print("✅ SearchManager逻辑一致，使用智能搜索模式选择")
        print("✅ 继承关系正确，方法功能正常")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
