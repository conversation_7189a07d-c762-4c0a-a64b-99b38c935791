#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试异步锁修复
验证AsyncGeminiAPIManager中的threading.Lock()已正确替换为asyncio.Lock()
"""

import asyncio
import sys
import traceback
from pathlib import Path

def test_async_lock_fix():
    """测试异步锁修复"""
    print("🧪 测试异步锁修复")
    print("=" * 60)
    
    try:
        # 导入修复后的模块
        from complete_report_generator import AsyncGeminiAPIManager, API_KEYS, MODEL_NAMES, GeminiModelConfig
        
        print("✅ 成功导入AsyncGeminiAPIManager")
        
        # 创建异步API管理器
        model_config = GeminiModelConfig()
        api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        
        print(f"✅ 成功创建AsyncGeminiAPIManager实例")
        print(f"   可用API密钥: {api_manager.total_keys} 个")
        
        # 检查锁的类型
        lock_type = type(api_manager.index_lock).__name__
        print(f"🔍 检查锁类型: {lock_type}")
        
        if lock_type == "Lock" and hasattr(api_manager.index_lock, '__aenter__'):
            print("✅ 锁类型正确: asyncio.Lock")
            return True
        elif lock_type == "Lock" and not hasattr(api_manager.index_lock, '__aenter__'):
            print("❌ 锁类型错误: 仍然是threading.Lock")
            return False
        else:
            print(f"⚠️ 未知锁类型: {lock_type}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        return False

async def test_async_methods():
    """测试异步方法"""
    print("\n🧪 测试异步方法调用")
    print("=" * 60)
    
    try:
        from complete_report_generator import AsyncGeminiAPIManager, API_KEYS, MODEL_NAMES, GeminiModelConfig
        
        # 创建异步API管理器
        model_config = GeminiModelConfig()
        api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        
        print("✅ 成功创建AsyncGeminiAPIManager实例")
        
        # 测试异步方法调用
        print("🔍 测试 _get_available_api_config() 方法...")
        api_config = await api_manager._get_available_api_config()
        
        if api_config:
            print(f"✅ 成功获取API配置: {api_config['api_name']}")
        else:
            print("⚠️ 暂时无可用API配置（这是正常的，因为API密钥可能无效）")
        
        print("🔍 测试 _reset_error_states() 方法...")
        await api_manager._reset_error_states()
        print("✅ 成功调用 _reset_error_states()")
        
        print("🔍 测试 _force_reset_all_apis() 方法...")
        await api_manager._force_reset_all_apis()
        print("✅ 成功调用 _force_reset_all_apis()")
        
        print("🔍 测试 _mark_api_error() 方法...")
        await api_manager._mark_api_error(0, "test_error")
        print("✅ 成功调用 _mark_api_error()")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步方法测试失败: {str(e)}")
        traceback.print_exc()
        return False

async def test_api_call_simulation():
    """模拟API调用测试"""
    print("\n🧪 模拟API调用测试")
    print("=" * 60)
    
    try:
        from complete_report_generator import AsyncGeminiAPIManager, API_KEYS, MODEL_NAMES, GeminiModelConfig
        
        # 创建异步API管理器
        model_config = GeminiModelConfig()
        api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES, model_config)
        
        print("✅ 成功创建AsyncGeminiAPIManager实例")
        
        # 模拟API调用（不实际调用API，只测试锁机制）
        print("🔍 模拟并发API调用测试...")
        
        async def mock_api_call(call_id):
            """模拟API调用"""
            try:
                # 获取API配置（测试异步锁）
                api_config = await api_manager._get_available_api_config()
                
                if api_config:
                    print(f"   📞 模拟调用 {call_id}: 使用 {api_config['api_name']}")
                    
                    # 模拟处理时间
                    await asyncio.sleep(0.1)
                    
                    # 模拟标记错误（测试异步锁）
                    await api_manager._mark_api_error(api_config['api_index'], f"mock_error_{call_id}")
                    
                    return f"调用 {call_id} 完成"
                else:
                    return f"调用 {call_id} 无可用API"
                    
            except Exception as e:
                return f"调用 {call_id} 失败: {str(e)}"
        
        # 并发执行多个模拟调用
        tasks = [mock_api_call(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        print("📊 并发调用结果:")
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"   ❌ 任务 {i}: {str(result)}")
            else:
                print(f"   ✅ 任务 {i}: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用模拟测试失败: {str(e)}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始异步锁修复测试")
    print("=" * 80)
    
    # 测试1: 基础锁类型检查
    test1_result = test_async_lock_fix()
    
    # 测试2: 异步方法调用
    test2_result = await test_async_methods()
    
    # 测试3: 并发API调用模拟
    test3_result = await test_api_call_simulation()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"   🔍 锁类型检查: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   🔄 异步方法调用: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   🚀 并发调用模拟: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    
    if all_passed:
        print("\n🎉 所有测试通过！异步锁修复成功！")
        print("✅ AsyncGeminiAPIManager 现在使用 asyncio.Lock() 而不是 threading.Lock()")
        print("✅ 所有相关方法已正确更新为异步方法")
        print("✅ 异步锁机制工作正常，不会阻塞事件循环")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
