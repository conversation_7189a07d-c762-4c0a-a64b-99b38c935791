#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复代码质量问题
1. 未使用的参数
2. 过度宽泛的异常捕获
"""

import re
import sys
from pathlib import Path

def fix_unused_parameters():
    """修复未使用的参数问题"""
    print("🔧 修复未使用的参数问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复context参数未使用的问题
    # 方案1: 使用context参数来增强任务目的提取
    old_method = '''    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的"""
        prompt_lower = prompt.lower()'''
    
    new_method = '''    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的"""
        prompt_lower = prompt.lower()
        
        # 使用context信息增强任务目的提取
        if context:
            # 如果有上下文信息，可以更精确地判断任务类型
            task_type = context.get('task_type', '')
            section_info = context.get('section_info', {})
            
            if task_type == 'framework_generation':
                return "🎯 统筹模型生成报告框架结构"
            elif task_type == 'content_generation' and section_info:
                section_title = section_info.get('title', '未知章节')
                section_level = section_info.get('level', '未知级别')
                return f"⚡ 执行模型生成第{section_level}级节点: {section_title}"
            elif task_type == 'review' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"🔍 统筹模型审核章节: {section_title}"
            elif task_type == 'optimization' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"✨ 统筹模型优化章节: {section_title}"'''
    
    if old_method in content:
        content = content.replace(old_method, new_method)
        print("   ✅ 修复了_extract_task_purpose方法中的context参数")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ 未使用参数修复完成")

def fix_broad_exception_handling():
    """修复过度宽泛的异常捕获"""
    print("🔧 修复过度宽泛的异常捕获...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要特殊处理的异常类型映射
    exception_mappings = [
        # API相关异常
        {
            'pattern': r'except Exception as e:\s*\n\s*error_msg = str\(e\)\.lower\(\)\s*\n\s*print\(f".*?API.*?失败.*?"\)',
            'replacement': '''except (ConnectionError, TimeoutError, ValueError, KeyError) as e:
                error_msg = str(e).lower()
                print(f"API调用失败: {error_msg}")
            except Exception as e:
                error_msg = str(e).lower()
                print(f"未知API错误: {error_msg}")''',
            'description': 'API调用异常'
        },
        
        # 文件操作异常
        {
            'pattern': r'except Exception as e:\s*\n\s*print\(f".*?读取.*?失败.*?"\)\s*\n\s*return ""',
            'replacement': '''except (FileNotFoundError, PermissionError, UnicodeDecodeError) as e:
                print(f"文件读取失败: {str(e)}")
                return ""
            except Exception as e:
                print(f"未知文件错误: {str(e)}")
                return ""''',
            'description': '文件读取异常'
        },
        
        # JSON解析异常
        {
            'pattern': r'except Exception as e:\s*\n\s*print\(f".*?JSON.*?失败.*?"\)',
            'replacement': '''except json.JSONDecodeError as e:
                print(f"JSON解析失败: {str(e)}")
            except (KeyError, TypeError) as e:
                print(f"JSON结构错误: {str(e)}")
            except Exception as e:
                print(f"未知JSON错误: {str(e)}")''',
            'description': 'JSON处理异常'
        }
    ]
    
    # 应用异常映射（这里只是示例，实际需要更精确的替换）
    changes_made = 0
    
    # 更安全的方法：逐个替换特定的异常处理模式
    specific_fixes = [
        # 修复图片匹配异常
        {
            'old': '''        except Exception as e:
            print(f"     ❌ 图片匹配出错: {str(e)}")
            return []''',
            'new': '''        except (ValueError, KeyError, AttributeError) as e:
            print(f"     ❌ 图片匹配参数错误: {str(e)}")
            return []
        except Exception as e:
            print(f"     ❌ 图片匹配未知错误: {str(e)}")
            return []'''
        },
        
        # 修复字体设置异常
        {
            'old': '''            except Exception as e:
                print(f"⚠️ 中文字体设置失败: {str(e)}")
                self._download_chinese_font()''',
            'new': '''            except (ImportError, OSError, RuntimeError) as e:
                print(f"⚠️ 中文字体设置失败: {str(e)}")
                self._download_chinese_font()
            except Exception as e:
                print(f"⚠️ 字体设置未知错误: {str(e)}")
                self._download_chinese_font()'''
        },
        
        # 修复checkpoint异常
        {
            'old': '''        except Exception as e:
            print(f"❌ 保存checkpoint失败: {str(e)}")
            return ""''',
            'new': '''        except (FileNotFoundError, PermissionError, OSError) as e:
            print(f"❌ checkpoint文件操作失败: {str(e)}")
            return ""
        except (json.JSONEncodeError, TypeError) as e:
            print(f"❌ checkpoint数据序列化失败: {str(e)}")
            return ""
        except Exception as e:
            print(f"❌ checkpoint保存未知错误: {str(e)}")
            return ""'''
        },
        
        # 修复API调用异常
        {
            'old': '''            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! ERROR with {api_name} using model {model_name}: {error_msg} !!!!!!")''',
            'new': '''            except (ConnectionError, TimeoutError) as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 网络错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")
            except (ValueError, KeyError) as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 参数错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")
            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 未知错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")'''
        }
    ]
    
    for fix in specific_fixes:
        if fix['old'] in content:
            content = content.replace(fix['old'], fix['new'])
            changes_made += 1
            print(f"   ✅ 修复了一个异常处理模式")
    
    print(f"   ✅ 异常处理修复完成，共修复 {changes_made} 处")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def add_specific_exception_imports():
    """添加特定异常类型的导入"""
    print("🔧 添加特定异常类型的导入...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在文件开头添加必要的导入
    import_section = '''import json
import re
import os
import sys
import time
import asyncio
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import traceback

# 添加特定异常类型的导入
import socket
from urllib.error import URLError, HTTPError
from json import JSONDecodeError'''
    
    # 查找现有的导入部分并替换
    if 'import json' in content and 'from json import JSONDecodeError' not in content:
        # 在现有导入后添加新的导入
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                insert_index = i
        
        if insert_index > -1:
            # 在最后一个导入语句后插入
            lines.insert(insert_index + 1, '')
            lines.insert(insert_index + 2, '# 特定异常类型导入')
            lines.insert(insert_index + 3, 'from json import JSONDecodeError')
            lines.insert(insert_index + 4, 'import socket')
            lines.insert(insert_index + 5, 'from urllib.error import URLError, HTTPError')
            
            content = '\n'.join(lines)
            print("   ✅ 添加了特定异常类型的导入")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def create_exception_handling_guidelines():
    """创建异常处理指南"""
    guidelines = '''# 异常处理指南

## 修复的问题

### 1. 未使用的参数
- **问题**: `_extract_task_purpose` 方法中的 `context` 参数被定义但从未使用
- **修复**: 使用 `context` 参数来增强任务目的提取的准确性

### 2. 过度宽泛的异常捕获
- **问题**: 大量使用 `except Exception as e:` 可能掩盖具体错误类型
- **修复**: 使用更具体的异常类型，提供更好的错误诊断

## 异常处理最佳实践

### 1. 使用具体的异常类型
```python
# ❌ 过度宽泛
try:
    # 代码
except Exception as e:
    print(f"错误: {e}")

# ✅ 具体明确
try:
    # 代码
except (FileNotFoundError, PermissionError) as e:
    print(f"文件操作错误: {e}")
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

### 2. 分层异常处理
```python
try:
    # 主要逻辑
except SpecificError as e:
    # 处理特定错误
    handle_specific_error(e)
except (Error1, Error2) as e:
    # 处理相关错误组
    handle_related_errors(e)
except Exception as e:
    # 处理未预期的错误
    log_unexpected_error(e)
    raise  # 重新抛出以便调试
```

### 3. 错误恢复策略
```python
try:
    primary_method()
except SpecificError:
    fallback_method()
except Exception as e:
    log_error(e)
    return default_value
```

## 常见异常类型

### 文件操作
- `FileNotFoundError`: 文件不存在
- `PermissionError`: 权限不足
- `UnicodeDecodeError`: 编码错误
- `OSError`: 操作系统相关错误

### 网络操作
- `ConnectionError`: 连接错误
- `TimeoutError`: 超时错误
- `URLError`: URL错误
- `HTTPError`: HTTP错误

### 数据处理
- `json.JSONDecodeError`: JSON解析错误
- `ValueError`: 值错误
- `KeyError`: 键错误
- `TypeError`: 类型错误
- `AttributeError`: 属性错误

### API调用
- `ImportError`: 模块导入错误
- `ModuleNotFoundError`: 模块未找到
- `RuntimeError`: 运行时错误
'''
    
    with open('异常处理指南.md', 'w', encoding='utf-8') as f:
        f.write(guidelines)
    
    print("   ✅ 创建了异常处理指南文档")

def main():
    """主修复函数"""
    print("🚀 开始修复代码质量问题")
    print("=" * 60)
    
    try:
        # 1. 修复未使用的参数
        fix_unused_parameters()
        
        # 2. 添加特定异常类型的导入
        add_specific_exception_imports()
        
        # 3. 修复过度宽泛的异常捕获
        fix_broad_exception_handling()
        
        # 4. 创建异常处理指南
        create_exception_handling_guidelines()
        
        print("\n" + "=" * 60)
        print("🎉 代码质量问题修复完成！")
        print("✅ 修复了未使用的参数问题")
        print("✅ 改进了异常处理的具体性")
        print("✅ 添加了异常处理指南")
        print("\n📋 建议:")
        print("1. 查看生成的'异常处理指南.md'了解最佳实践")
        print("2. 在后续开发中遵循具体异常处理原则")
        print("3. 定期检查和改进异常处理逻辑")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 修复执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
