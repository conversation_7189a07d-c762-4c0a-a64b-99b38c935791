#!/usr/bin/env python3
"""
异常处理优化脚本
将过于宽泛的异常捕获改为更具体的异常处理
"""

import re
import os
from pathlib import Path

class ExceptionHandlingFixer:
    """异常处理修复器"""
    
    def __init__(self):
        self.file_operations_patterns = [
            # 文件操作相关的异常
            (r'(open\(|\.read\(|\.write\(|Path\()', ['FileNotFoundError', 'PermissionError', 'OSError']),
            (r'(json\.loads|json\.dumps|json\.load)', ['json.JSONDecodeError']),
            (r'(\.to_csv|\.read_csv|pd\.)', ['pandas.errors.ParserError', 'pandas.errors.EmptyDataError']),
            (r'(import |from .* import)', ['ImportError', 'ModuleNotFoundError']),
            (r'(requests\.|http\.)', ['requests.exceptions.RequestException', 'requests.exceptions.Timeout']),
            (r'(\.get\(|\.pop\(|\[.*\])', ['KeyError', 'IndexError']),
            (r'(int\(|float\(|str\()', ['ValueError', 'TypeError']),
        ]
    
    def analyze_exception_context(self, code_block: str) -> list:
        """分析代码块，确定应该捕获的具体异常类型"""
        specific_exceptions = []
        
        for pattern, exceptions in self.file_operations_patterns:
            if re.search(pattern, code_block, re.IGNORECASE):
                specific_exceptions.extend(exceptions)
        
        # 去重并保持顺序
        return list(dict.fromkeys(specific_exceptions))
    
    def generate_improved_exception_handling(self, try_block: str, except_block: str) -> str:
        """生成改进的异常处理代码"""
        specific_exceptions = self.analyze_exception_context(try_block)
        
        if not specific_exceptions:
            # 如果无法确定具体异常，保持原样但添加注释
            return except_block.replace(
                'except Exception as e:',
                'except Exception as e:  # TODO: 需要更具体的异常类型'
            )
        
        # 生成具体的异常处理
        improved_handling = []
        
        for exc_type in specific_exceptions:
            improved_handling.append(f"        except {exc_type} as e:")
            improved_handling.append(f"            print(f\"⚠️ {exc_type}: {{str(e)}}\")")
            if 'return' in except_block:
                # 提取原始的返回值
                return_match = re.search(r'return (.+)', except_block)
                if return_match:
                    improved_handling.append(f"            {return_match.group(0)}")
        
        # 添加通用异常作为兜底
        improved_handling.append("        except Exception as e:")
        improved_handling.append("            print(f\"⚠️ 未预期的错误: {str(e)}\")")
        if 'return' in except_block:
            return_match = re.search(r'return (.+)', except_block)
            if return_match:
                improved_handling.append(f"            {return_match.group(0)}")
        
        return '\n'.join(improved_handling)

def create_exception_handling_examples():
    """创建异常处理改进示例"""
    examples = {
        "文件操作": {
            "before": """
try:
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    return content
except Exception as e:
    print(f"读取文件失败: {str(e)}")
    return ""
""",
            "after": """
try:
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    return content
except FileNotFoundError as e:
    print(f"⚠️ 文件不存在: {str(e)}")
    return ""
except PermissionError as e:
    print(f"⚠️ 文件权限不足: {str(e)}")
    return ""
except UnicodeDecodeError as e:
    print(f"⚠️ 文件编码错误: {str(e)}")
    # 尝试其他编码
    try:
        with open(file_path, 'r', encoding='gbk') as f:
            return f.read()
    except Exception:
        return ""
except OSError as e:
    print(f"⚠️ 系统IO错误: {str(e)}")
    return ""
except Exception as e:
    print(f"⚠️ 未预期的文件读取错误: {str(e)}")
    return ""
"""
        },
        
        "JSON处理": {
            "before": """
try:
    data = json.loads(response_text)
    return data
except Exception as e:
    print(f"JSON解析失败: {str(e)}")
    return {}
""",
            "after": """
try:
    data = json.loads(response_text)
    return data
except json.JSONDecodeError as e:
    print(f"⚠️ JSON格式错误: {str(e)}")
    print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
    print(f"   响应内容: {response_text[:200]}...")
    return {}
except TypeError as e:
    print(f"⚠️ JSON数据类型错误: {str(e)}")
    return {}
except Exception as e:
    print(f"⚠️ 未预期的JSON处理错误: {str(e)}")
    return {}
"""
        },
        
        "API调用": {
            "before": """
try:
    response = requests.get(url, timeout=30)
    return response.json()
except Exception as e:
    print(f"API调用失败: {str(e)}")
    return None
""",
            "after": """
try:
    response = requests.get(url, timeout=30)
    response.raise_for_status()  # 检查HTTP状态码
    return response.json()
except requests.exceptions.Timeout as e:
    print(f"⚠️ API请求超时: {str(e)}")
    return None
except requests.exceptions.ConnectionError as e:
    print(f"⚠️ 网络连接错误: {str(e)}")
    return None
except requests.exceptions.HTTPError as e:
    print(f"⚠️ HTTP错误: {e.response.status_code} - {str(e)}")
    return None
except json.JSONDecodeError as e:
    print(f"⚠️ API响应JSON格式错误: {str(e)}")
    return None
except Exception as e:
    print(f"⚠️ 未预期的API调用错误: {str(e)}")
    return None
"""
        },
        
        "数据处理": {
            "before": """
try:
    result = data[key]
    value = int(result)
    return value
except Exception as e:
    print(f"数据处理失败: {str(e)}")
    return 0
""",
            "after": """
try:
    result = data[key]
    value = int(result)
    return value
except KeyError as e:
    print(f"⚠️ 缺少必需的数据字段: {str(e)}")
    return 0
except ValueError as e:
    print(f"⚠️ 数据格式错误，无法转换为整数: {str(e)}")
    return 0
except TypeError as e:
    print(f"⚠️ 数据类型错误: {str(e)}")
    return 0
except Exception as e:
    print(f"⚠️ 未预期的数据处理错误: {str(e)}")
    return 0
"""
        }
    }
    
    return examples

def main():
    """主函数：演示异常处理改进"""
    print("🔧 异常处理优化示例")
    print("=" * 60)
    
    examples = create_exception_handling_examples()
    
    for category, example in examples.items():
        print(f"\n📋 {category}异常处理优化")
        print("-" * 40)
        print("❌ 修改前（过于宽泛）:")
        print(example["before"])
        print("✅ 修改后（具体明确）:")
        print(example["after"])
    
    print("\n🎯 异常处理最佳实践:")
    print("1. 优先捕获具体的异常类型")
    print("2. 为每种异常提供有意义的错误信息")
    print("3. 在适当时提供恢复策略")
    print("4. 使用Exception作为最后的兜底")
    print("5. 记录足够的上下文信息用于调试")

if __name__ == "__main__":
    main()
