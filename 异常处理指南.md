# 异常处理指南

## 修复的问题

### 1. 未使用的参数
- **问题**: `_extract_task_purpose` 方法中的 `context` 参数被定义但从未使用
- **修复**: 使用 `context` 参数来增强任务目的提取的准确性

### 2. 过度宽泛的异常捕获
- **问题**: 大量使用 `except Exception as e:` 可能掩盖具体错误类型
- **修复**: 使用更具体的异常类型，提供更好的错误诊断

## 异常处理最佳实践

### 1. 使用具体的异常类型
```python
# ❌ 过度宽泛
try:
    # 代码
except Exception as e:
    print(f"错误: {e}")

# ✅ 具体明确
try:
    # 代码
except (FileNotFoundError, PermissionError) as e:
    print(f"文件操作错误: {e}")
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

### 2. 分层异常处理
```python
try:
    # 主要逻辑
except SpecificError as e:
    # 处理特定错误
    handle_specific_error(e)
except (Error1, Error2) as e:
    # 处理相关错误组
    handle_related_errors(e)
except Exception as e:
    # 处理未预期的错误
    log_unexpected_error(e)
    raise  # 重新抛出以便调试
```

### 3. 错误恢复策略
```python
try:
    primary_method()
except SpecificError:
    fallback_method()
except Exception as e:
    log_error(e)
    return default_value
```

## 常见异常类型

### 文件操作
- `FileNotFoundError`: 文件不存在
- `PermissionError`: 权限不足
- `UnicodeDecodeError`: 编码错误
- `OSError`: 操作系统相关错误

### 网络操作
- `ConnectionError`: 连接错误
- `TimeoutError`: 超时错误
- `URLError`: URL错误
- `HTTPError`: HTTP错误

### 数据处理
- `json.JSONDecodeError`: JSON解析错误
- `ValueError`: 值错误
- `KeyError`: 键错误
- `TypeError`: 类型错误
- `AttributeError`: 属性错误

### API调用
- `ImportError`: 模块导入错误
- `ModuleNotFoundError`: 模块未找到
- `RuntimeError`: 运行时错误
