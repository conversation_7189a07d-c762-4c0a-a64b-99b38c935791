#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试代码质量修复效果
验证未使用参数和异常处理的改进
"""

import sys
import traceback
import inspect

def test_context_parameter_usage():
    """测试context参数的使用"""
    print("🧪 测试context参数的使用")
    print("=" * 60)
    
    try:
        from complete_report_generator import BaseGeminiAPIManager
        
        # 创建基类实例
        manager = BaseGeminiAPIManager()
        
        print("✅ 成功创建BaseGeminiAPIManager实例")
        
        # 测试不带context的调用
        prompt1 = "生成报告框架的JSON结构"
        result1 = manager._extract_task_purpose(prompt1)
        print(f"📝 无context调用: '{prompt1[:30]}...' → {result1}")
        
        # 测试带context的调用 - 框架生成
        context1 = {
            'task_type': 'framework_generation'
        }
        result2 = manager._extract_task_purpose(prompt1, context1)
        print(f"📝 有context调用(框架): '{prompt1[:30]}...' → {result2}")
        
        # 测试带context的调用 - 内容生成
        prompt2 = "生成第3级节点的详细内容"
        context2 = {
            'task_type': 'content_generation',
            'section_info': {
                'title': '技术发展趋势',
                'level': '3'
            }
        }
        result3 = manager._extract_task_purpose(prompt2, context2)
        print(f"📝 有context调用(内容): '{prompt2[:30]}...' → {result3}")
        
        # 测试带context的调用 - 审核
        prompt3 = "审核章节内容质量"
        context3 = {
            'task_type': 'review',
            'section_info': {
                'title': '市场分析'
            }
        }
        result4 = manager._extract_task_purpose(prompt3, context3)
        print(f"📝 有context调用(审核): '{prompt3[:30]}...' → {result4}")
        
        # 验证context确实被使用了
        if result2 != result1:
            print("✅ context参数被正确使用，影响了结果")
            return True
        else:
            print("❌ context参数可能未被正确使用")
            return False
        
    except Exception as e:
        print(f"❌ context参数测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_specific_exception_handling():
    """测试具体异常处理"""
    print("\n🧪 测试具体异常处理")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        print("✅ 成功创建CompleteReportGenerator实例")
        
        # 测试文件读取异常处理
        print("📁 测试文件读取异常处理...")
        
        # 尝试读取不存在的文件
        non_existent_file = "non_existent_file.txt"
        result = generator._read_text_file(non_existent_file)
        
        if "读取文本文件失败" in result:
            print("✅ 文件读取异常处理正常")
        else:
            print("⚠️ 文件读取异常处理可能需要改进")
        
        # 测试JSON解析异常处理
        print("📄 测试JSON解析异常处理...")
        
        # 尝试解析无效的JSON
        invalid_json = "{ invalid json content"
        try:
            import json
            json.loads(invalid_json)
        except json.JSONDecodeError:
            print("✅ JSON解析异常类型正确")
        except Exception as e:
            print(f"⚠️ JSON解析异常类型: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常处理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_import_statements():
    """测试导入语句"""
    print("\n🧪 测试特定异常类型导入")
    print("=" * 60)
    
    try:
        # 检查是否能正确导入特定异常类型
        from json import JSONDecodeError
        print("✅ JSONDecodeError 导入成功")
        
        import socket
        print("✅ socket 模块导入成功")
        
        from urllib.error import URLError, HTTPError
        print("✅ URLError, HTTPError 导入成功")
        
        # 测试这些异常类型是否可用
        try:
            raise JSONDecodeError("测试", "doc", 0)
        except JSONDecodeError:
            print("✅ JSONDecodeError 异常类型工作正常")
        
        try:
            raise URLError("测试")
        except URLError:
            print("✅ URLError 异常类型工作正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 导入测试失败: {str(e)}")
        return False

def analyze_exception_patterns():
    """分析异常处理模式"""
    print("\n🧪 分析异常处理模式")
    print("=" * 60)
    
    try:
        import re
        from pathlib import Path
        
        file_path = Path("complete_report_generator.py")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计异常处理模式
        broad_exceptions = len(re.findall(r'except Exception as e:', content))
        specific_exceptions = len(re.findall(r'except \([^)]+\) as e:', content))
        json_exceptions = len(re.findall(r'except.*JSONDecodeError', content))
        file_exceptions = len(re.findall(r'except.*FileNotFoundError', content))
        
        print(f"📊 异常处理统计:")
        print(f"   宽泛异常处理 (Exception): {broad_exceptions}")
        print(f"   具体异常处理 (多类型): {specific_exceptions}")
        print(f"   JSON异常处理: {json_exceptions}")
        print(f"   文件异常处理: {file_exceptions}")
        
        # 计算改进比例
        total_exceptions = broad_exceptions + specific_exceptions
        if total_exceptions > 0:
            improvement_ratio = specific_exceptions / total_exceptions * 100
            print(f"   具体异常处理比例: {improvement_ratio:.1f}%")
            
            if improvement_ratio > 10:  # 如果有超过10%的具体异常处理
                print("✅ 异常处理改进效果良好")
                return True
            else:
                print("⚠️ 异常处理仍有改进空间")
                return False
        else:
            print("⚠️ 未找到异常处理代码")
            return False
        
    except Exception as e:
        print(f"❌ 异常模式分析失败: {str(e)}")
        return False

def test_method_signature_improvements():
    """测试方法签名改进"""
    print("\n🧪 测试方法签名改进")
    print("=" * 60)
    
    try:
        from complete_report_generator import BaseGeminiAPIManager
        
        # 检查方法签名
        method = getattr(BaseGeminiAPIManager, '_extract_task_purpose')
        signature = inspect.signature(method)
        
        print(f"📝 方法签名: {signature}")
        
        # 检查参数
        params = list(signature.parameters.keys())
        print(f"📋 参数列表: {params}")
        
        if 'context' in params:
            context_param = signature.parameters['context']
            print(f"📌 context参数: {context_param}")
            
            # 检查默认值
            if context_param.default is None:
                print("✅ context参数有正确的默认值")
            else:
                print("⚠️ context参数默认值可能需要检查")
            
            # 检查类型注解
            if context_param.annotation != inspect.Parameter.empty:
                print(f"✅ context参数有类型注解: {context_param.annotation}")
            else:
                print("⚠️ context参数缺少类型注解")
            
            return True
        else:
            print("❌ context参数未找到")
            return False
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始代码质量修复效果测试")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("context参数使用", test_context_parameter_usage),
        ("具体异常处理", test_specific_exception_handling),
        ("导入语句", test_import_statements),
        ("异常处理模式分析", analyze_exception_patterns),
        ("方法签名改进", test_method_signature_improvements),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试执行失败: {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！代码质量修复效果良好！")
        print("✅ context参数被正确使用")
        print("✅ 异常处理更加具体和精确")
        print("✅ 导入语句完整正确")
        print("✅ 代码质量得到显著提升")
    else:
        print("\n⚠️ 部分测试失败，但核心修复已完成")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
