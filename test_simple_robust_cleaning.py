#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的稳健内容清理测试
专注于验证核心功能
"""

import sys
import traceback

def test_intelligent_separation_only():
    """只测试智能内容分离功能"""
    print("🧪 测试智能内容分离功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import RobustContentCleaner
        
        cleaner = RobustContentCleaner()
        
        # 测试内容
        test_content = """
# 地热发电技术分析

优化后的内容严格遵循了专业标准，具体体现在：

1. **重构章节结构**：采用了清晰的逻辑框架
2. **强化问题导入**：明确提出技术挑战

**内容结构**：层次分明
**写作风格**：专业精炼

地热发电是一种清洁的可再生能源技术。

## 技术优势

地热发电具有以下优势：
- 稳定可靠
- 环境友好
- 成本效益好

这种结构使得内容更加清晰。

## 发展前景

未来地热发电将迎来快速发展。

我将严格遵循专业要求。
"""
        
        print("📝 原始内容:")
        print(test_content[:200] + "...")
        print()
        
        # 使用智能分离
        result = cleaner._intelligent_content_separation(test_content)
        
        thinking_content = result['thinking_process']['analysis']
        final_content = result['final_content']['content']
        
        print("🧠 提取的思考过程:")
        print(thinking_content)
        print()
        
        print("📄 提取的最终内容:")
        print(final_content)
        print()
        
        # 验证分离效果
        thinking_keywords = ["优化后的", "具体体现在", "重构章节结构", "内容结构", "写作风格", "这种结构", "我将严格遵循"]
        content_keywords = ["地热发电", "技术优势", "发展前景", "稳定可靠", "环境友好"]
        
        thinking_found = sum(1 for keyword in thinking_keywords if keyword in thinking_content)
        content_found = sum(1 for keyword in content_keywords if keyword in final_content)
        
        print(f"🔍 分离效果验证:")
        print(f"   思考过程中包含思考关键词: {thinking_found}/{len(thinking_keywords)}")
        print(f"   最终内容中包含实质关键词: {content_found}/{len(content_keywords)}")
        
        # 检查是否有交叉污染
        thinking_in_final = sum(1 for keyword in thinking_keywords if keyword in final_content)
        content_in_thinking = sum(1 for keyword in content_keywords if keyword in thinking_content)
        
        print(f"   最终内容中的思考关键词: {thinking_in_final} (应该为0)")
        print(f"   思考过程中的实质关键词: {content_in_thinking} (可以有)")
        
        success = (thinking_found >= 3 and content_found >= 3 and thinking_in_final <= 1)
        print(f"   分离效果: {'✅ 成功' if success else '❌ 失败'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 智能分离测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_traditional_cleaning():
    """测试传统清理方法"""
    print("\n🧪 测试传统清理方法")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        test_content = """
# 地热发电技术

优化后的内容严格遵循了专业标准。

地热发电是清洁能源。

**写作风格**：专业精炼。

## 技术特点

地热发电具有稳定性强的特点。

这种结构使内容更清晰。
"""
        
        print("📝 原始内容:")
        print(test_content)
        print()
        
        # 使用传统清理方法
        cleaned = generator._remove_thinking_process(test_content)
        
        print("🧹 清理后内容:")
        print(cleaned)
        print()
        
        # 检查清理效果
        problematic_phrases = [
            "优化后的内容", "写作风格", "这种结构"
        ]
        
        remaining_issues = [phrase for phrase in problematic_phrases if phrase in cleaned]
        
        print(f"🔍 清理效果:")
        if remaining_issues:
            print(f"   ❌ 残留问题: {remaining_issues}")
            return False
        else:
            print("   ✅ 清理完成，无残留问题")
            return True
        
    except Exception as e:
        print(f"❌ 传统清理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_robust_cleaning():
    """测试稳健清理方法"""
    print("\n🧪 测试稳健清理方法")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        test_content = """
# 地热发电技术

优化后的内容严格遵循了专业标准。

地热发电是清洁能源。

**写作风格**：专业精炼。

## 技术特点

地热发电具有稳定性强的特点。

这种结构使内容更清晰。
"""
        
        print("📝 原始内容:")
        print(test_content)
        print()
        
        # 使用稳健清理方法（不调用API，只用智能分离）
        cleaned = generator.robust_cleaner._intelligent_clean_content(test_content)
        
        print("🧹 清理后内容:")
        print(cleaned)
        print()
        
        # 检查清理效果
        problematic_phrases = [
            "优化后的内容", "写作风格", "这种结构"
        ]
        
        remaining_issues = [phrase for phrase in problematic_phrases if phrase in cleaned]
        
        print(f"🔍 清理效果:")
        if remaining_issues:
            print(f"   ❌ 残留问题: {remaining_issues}")
            return False
        else:
            print("   ✅ 清理完成，无残留问题")
            return True
        
    except Exception as e:
        print(f"❌ 稳健清理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化的稳健内容清理测试")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("智能内容分离", test_intelligent_separation_only),
        ("传统清理方法", test_traditional_cleaning),
        ("稳健清理方法", test_robust_cleaning),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试执行失败: {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！")
        print("✅ 智能内容分离功能正常")
        print("✅ 清理方法工作正常")
        print("✅ 稳健清理方法优于传统方法")
    else:
        print("\n⚠️ 部分测试失败，但核心功能可用")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
