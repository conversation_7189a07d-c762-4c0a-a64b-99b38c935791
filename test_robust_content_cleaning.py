#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试稳健的内容清理方法
验证新的结构化清理方法相比传统正则表达式方法的优势
"""

import sys
import traceback
from pathlib import Path

def test_traditional_vs_robust_cleaning():
    """对比传统清理方法和稳健清理方法"""
    print("🧪 测试传统清理方法 vs 稳健清理方法")
    print("=" * 80)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器（使用同步模式以简化测试）
        generator = CompleteReportGenerator(use_async=False)
        
        print("✅ 成功创建报告生成器")
        
        # 测试内容：包含各种思考过程模式
        test_content = """
# 地热发电技术发展现状

优化后的内容严格遵循了专业报告的写作标准，具体体现在：

1. **重构章节结构**：采用了"总-分-总"的逻辑框架，将核心观点前置，使读者能够快速把握章节要点。

2. **强化问题导入**：在开篇明确提出地热发电面临的技术挑战，为后续分析奠定基础。

**内容结构**：采用递进式逻辑，层次分明。
**写作风格**：语言精炼专业，并使用了前瞻性表述。
**格式规范**：使用了多级标题和数据表格，极大提升了信息传递效率和专业感。
**专业标准**：精准运用了能源领域术语，体现了分析深度。

地热发电作为可再生能源的重要组成部分，近年来在全球范围内得到快速发展。

## 技术发展历程

地热发电技术经历了从简单的干蒸汽发电到现代的双循环发电系统的演进过程。

### 早期发展阶段

20世纪初，意大利建成了世界上第一座地热发电站。

### 现代发展阶段

21世纪以来，地热发电技术不断创新，效率显著提升。

## 主要技术类型

目前主要的地热发电技术包括：

1. 干蒸汽发电技术
2. 闪蒸发电技术  
3. 双循环发电技术

这种结构使得内容层次分明，便于读者理解。

### 优化分析

原有的内容将被剥离，以下是优化后的完整章节内容：

优化后的版本在结构上采用了递进式逻辑，在风格上强化了数据驱动的特点，在专业性上则力求分析更深入、术语更精准。

## 技术发展趋势

未来地热发电技术将朝着更高效、更环保的方向发展。

我将严格遵循专业报告的写作要求，确保内容的科学性和准确性。
"""
        
        print("📝 测试内容长度:", len(test_content))
        print("📝 测试内容预览:")
        print(test_content[:300] + "...")
        print()
        
        # 测试1: 传统清理方法
        print("🔧 测试1: 传统正则表达式清理方法")
        print("-" * 50)
        
        traditional_cleaned = generator._remove_thinking_process(test_content)
        
        print(f"   原始长度: {len(test_content):,} 字符")
        print(f"   清理后长度: {len(traditional_cleaned):,} 字符")
        print(f"   清理比例: {(len(test_content) - len(traditional_cleaned)) / len(test_content) * 100:.1f}%")
        print()
        
        # 测试2: 稳健清理方法
        print("🚀 测试2: 稳健结构化清理方法")
        print("-" * 50)
        
        robust_cleaned = generator._remove_thinking_process_robust(test_content)
        
        print(f"   原始长度: {len(test_content):,} 字符")
        print(f"   清理后长度: {len(robust_cleaned):,} 字符")
        print(f"   清理比例: {(len(test_content) - len(robust_cleaned)) / len(test_content) * 100:.1f}%")
        print()
        
        # 验证清理效果
        print("🔍 清理效果验证")
        print("-" * 50)
        
        # 检查问题短语
        problematic_phrases = [
            "优化后的内容严格遵循",
            "具体体现在：",
            "**重构章节结构**：",
            "**强化问题导入",
            "**内容结构**：",
            "**写作风格**：",
            "**格式规范**：",
            "**专业标准**：",
            "### 优化分析",
            "优化后的版本在结构上采用了",
            "我将严格遵循",
            "这种结构使得"
        ]
        
        print("传统方法残留问题:")
        traditional_issues = [phrase for phrase in problematic_phrases if phrase in traditional_cleaned]
        if traditional_issues:
            for issue in traditional_issues:
                print(f"   ❌ {issue}")
        else:
            print("   ✅ 无残留问题")
        
        print("\n稳健方法残留问题:")
        robust_issues = [phrase for phrase in problematic_phrases if phrase in robust_cleaned]
        if robust_issues:
            for issue in robust_issues:
                print(f"   ❌ {issue}")
        else:
            print("   ✅ 无残留问题")
        
        # 内容质量对比
        print("\n📊 内容质量对比")
        print("-" * 50)
        
        # 计算有效内容比例
        def count_effective_content(text):
            lines = text.split('\n')
            effective_lines = 0
            for line in lines:
                line = line.strip()
                if line and line.startswith('#'):
                    effective_lines += 1
                elif line and not any(keyword in line for keyword in ["优化", "分析", "体现在", "结构上采用"]):
                    effective_lines += 1
            return effective_lines
        
        traditional_effective = count_effective_content(traditional_cleaned)
        robust_effective = count_effective_content(robust_cleaned)
        
        print(f"传统方法有效内容行数: {traditional_effective}")
        print(f"稳健方法有效内容行数: {robust_effective}")
        
        # 展示清理后的内容
        print("\n📄 传统方法清理结果预览:")
        print(traditional_cleaned[:500] + "...")
        
        print("\n📄 稳健方法清理结果预览:")
        print(robust_cleaned[:500] + "...")
        
        # 评估结果
        traditional_score = len(traditional_issues) == 0 and traditional_effective > 5
        robust_score = len(robust_issues) == 0 and robust_effective > 5
        
        print(f"\n🏆 评估结果:")
        print(f"   传统方法: {'✅ 通过' if traditional_score else '❌ 失败'}")
        print(f"   稳健方法: {'✅ 通过' if robust_score else '❌ 失败'}")
        
        return traditional_score, robust_score
        
    except Exception as e:
        print(f"❌ 清理方法对比测试失败: {str(e)}")
        traceback.print_exc()
        return False, False

def test_structured_content_generation():
    """测试结构化内容生成"""
    print("\n🧪 测试结构化内容生成")
    print("=" * 80)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print("✅ 成功创建报告生成器")
        
        # 测试结构化生成
        test_prompt = "请生成一段关于地热发电技术优势的内容，要求专业、简洁"
        
        print(f"📝 测试prompt: {test_prompt}")
        print()
        
        # 使用结构化生成器
        print("🚀 使用结构化生成器...")
        structured_result = generator.structured_generator.generate_structured_content(
            test_prompt, "technology_analysis"
        )
        
        print("📊 结构化生成结果:")
        print(f"   思考过程长度: {len(str(structured_result.get('thinking_process', {})))}")
        print(f"   最终内容长度: {len(structured_result.get('final_content', {}).get('content', ''))}")
        
        # 展示结果
        final_content = structured_result.get('final_content', {}).get('content', '')
        thinking_process = structured_result.get('thinking_process', {})
        
        print("\n🧠 思考过程:")
        print(f"   分析: {thinking_process.get('analysis', 'N/A')[:100]}...")
        print(f"   优化说明: {thinking_process.get('optimization_notes', 'N/A')[:100]}...")
        
        print("\n📄 最终内容:")
        print(final_content[:300] + "...")
        
        # 验证分离效果
        has_thinking_in_final = any(keyword in final_content.lower() for keyword in [
            "优化", "分析", "体现在", "具体", "思考", "考虑"
        ])
        
        print(f"\n🔍 分离效果验证:")
        print(f"   最终内容是否包含思考过程: {'❌ 是' if has_thinking_in_final else '✅ 否'}")
        
        return not has_thinking_in_final
        
    except Exception as e:
        print(f"❌ 结构化内容生成测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_intelligent_content_separation():
    """测试智能内容分离"""
    print("\n🧪 测试智能内容分离")
    print("=" * 80)
    
    try:
        from complete_report_generator import RobustContentCleaner
        
        cleaner = RobustContentCleaner()
        
        # 测试内容
        mixed_content = """
# 地热发电市场分析

优化后的内容严格遵循了市场分析的标准框架。

地热发电市场在全球范围内呈现快速增长态势。

**写作风格**：采用数据驱动的分析方法。

根据最新统计数据，2023年全球地热发电装机容量达到15.4GW。

这种结构使得分析更加清晰。

## 市场规模

全球地热发电市场预计将在2030年达到200亿美元。

我将严格遵循专业分析的要求。
"""
        
        print("📝 测试混合内容:")
        print(mixed_content[:200] + "...")
        print()
        
        # 使用智能分离
        result = cleaner._intelligent_content_separation(mixed_content)
        
        thinking_content = result['thinking_process']['analysis']
        final_content = result['final_content']['content']
        
        print("🧠 提取的思考过程:")
        print(thinking_content[:200] + "...")
        print()
        
        print("📄 提取的最终内容:")
        print(final_content[:300] + "...")
        print()
        
        # 验证分离效果
        thinking_keywords = ["优化后的", "写作风格", "这种结构", "我将严格遵循"]
        content_keywords = ["地热发电市场", "装机容量", "市场规模", "200亿美元"]
        
        thinking_in_thinking = sum(1 for keyword in thinking_keywords if keyword in thinking_content)
        content_in_content = sum(1 for keyword in content_keywords if keyword in final_content)
        
        print(f"🔍 分离效果验证:")
        print(f"   思考过程中包含思考关键词: {thinking_in_thinking}/{len(thinking_keywords)}")
        print(f"   最终内容中包含实质关键词: {content_in_content}/{len(content_keywords)}")
        
        success = thinking_in_thinking >= 2 and content_in_content >= 2
        print(f"   分离效果: {'✅ 成功' if success else '❌ 失败'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 智能内容分离测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始稳健内容清理方法测试")
    print("=" * 100)
    
    # 执行所有测试
    tests = [
        ("传统vs稳健清理对比", test_traditional_vs_robust_cleaning),
        ("结构化内容生成", test_structured_content_generation),
        ("智能内容分离", test_intelligent_content_separation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "传统vs稳健清理对比":
                traditional_result, robust_result = test_func()
                results[f"{test_name}_传统"] = traditional_result
                results[f"{test_name}_稳健"] = robust_result
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试执行失败: {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 100)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！稳健内容清理方法工作正常！")
        print("✅ 结构化方法有效分离思考过程和最终内容")
        print("✅ 智能清理方法优于传统正则表达式方法")
        print("✅ 内容质量得到显著提升")
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
